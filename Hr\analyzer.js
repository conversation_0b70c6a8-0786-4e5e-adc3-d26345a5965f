const form = document.getElementById('resumeForm');
const successMsg = document.getElementById('successMsg');

form.addEventListener('submit', function(e) {
  e.preventDefault();

  // Simulate form submission (backend not connected)
  console.log('Form submitted!');
  console.log('Name:', document.getElementById('name').value);
  console.log('Email:', document.getElementById('email').value);
  console.log('Phone:', document.getElementById('phone').value);
  console.log('Skills:', document.getElementById('skills').value);
  console.log('Education:', document.getElementById('education').value);
  console.log('Resume File:', document.getElementById('resume').files[0]);

  form.reset();
  successMsg.classList.remove('hidden');
});

function analyzeAnother() {
    document.getElementById('analysisSection').style.display = 'none';
    document.querySelector('.upload-section').style.display = 'block';
    document.getElementById('fileName').innerHTML = '';
}

async function getAtsScoreFromBackend(file, jobDescription) {
    // Placeholder for real backend call
    // Example: Use fetch to POST file and job description to your API
    // const formData = new FormData();
    // formData.append('resume', file);
    // formData.append('jobDescription', jobDescription);
    // const response = await fetch('/api/analyze', { method: 'POST', body: formData });
    // const data = await response.json();
    // return data.atsScore;

    // Simulated score for now
    return Math.floor(Math.random() * 41) + 60;
}

async function showAtsResults(files) {
    atsResultsSection.style.display = 'block';
    atsResultsTable.innerHTML = '';
    for (const file of files) {
        const atsScore = await getAtsScoreFromBackend(file, jobDescription.value);
        atsResultsTable.innerHTML += `
            <tr>
                <td style="padding:0.5rem 1rem;">${file.name}</td>
                <td style="padding:0.5rem 1rem;">
                    <span style="font-weight:bold;color:${atsScore>85?'#28a745':atsScore>70?'#ffc107':'#dc3545'}">${atsScore}%</span>
                </td>
            </tr>
        `;
    }
    analysisSection.style.display = 'block';
}
