{"liveServer.settings.port": 3000, "liveServer.settings.root": "/", "liveServer.settings.CustomBrowser": "chrome", "liveServer.settings.AdvanceCustomBrowserCmdLine": "", "liveServer.settings.NoBrowser": false, "liveServer.settings.ignoreFiles": [".vscode/**", "**/*.scss", "**/*.sass", "**/*.ts"], "emmet.includeLanguages": {"javascript": "javascriptreact"}, "html.format.indentInnerHtml": true, "html.format.preserveNewLines": true, "html.format.wrapLineLength": 120, "css.validate": true, "css.lint.unknownAtRules": "ignore", "javascript.validate.enable": true, "javascript.format.enable": true, "javascript.format.semicolons": "insert", "javascript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": true}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.wordWrap": "on", "editor.rulers": [80, 120], "editor.minimap.enabled": true, "editor.suggestSelection": "first", "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true}, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.fontSize": 14, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "breadcrumbs.enabled": true, "html.completion.attributeDefaultValue": "doublequotes", "css.completion.triggerPropertyValueCompletion": true, "javascript.preferences.quoteStyle": "single", "prettier.tabWidth": 2, "prettier.useTabs": false, "prettier.singleQuote": true, "prettier.trailingComma": "es5", "prettier.printWidth": 100, "git.enableSmartCommit": true, "git.confirmSync": false, "extensions.ignoreRecommendations": false, "problems.showCurrentInStatus": true, "debug.openDebug": "openOnDebugBreak", "telemetry.telemetryLevel": "off"}