<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Candidate Data Demo</title>
    <link rel="stylesheet" href="canditates.css">
    <link rel="stylesheet" href="canditate.css">
    <style>
        body {
            min-height: 100vh;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            /* Vibrant animated gradient background */
            background: linear-gradient(120deg, #ffb347 0%, #ff5e62 50%, #764ba2 100%);
            background-size: 200% 200%;
            animation: cubeai-bg-move 8s ease-in-out infinite;
        }
        @keyframes cubeai-bg-move {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        h2 {
            color: #fff;
            text-shadow: 2px 2px 12px #764ba2cc, 0 2px 8px #ff5e6299;
            letter-spacing: 2px;
            margin-top: 32px;
            margin-bottom: 24px;
            text-align: center;
        }
        /* Optional: add a subtle card effect for the table */
        #candidateTable {
            background: rgba(255,255,255,0.96);
            border-radius: 18px;
            box-shadow: 0 8px 32px #764ba255;
            margin: 0 auto 32px auto;
            max-width: 1100px;
        }
        input#candidateSearch {
            display: block;
            margin: 0 auto 24px auto;
            border-radius: 8px;
            border: 2px solid #ff5e62;
            font-size: 1.1rem;
            box-shadow: 0 2px 8px #ffb34733;
        }
    </style>
</head>
<body>
    <h2>CubeAI Solutions - Candidate Data </h2>
    <input type="text" id="candidateSearch" onkeyup="filterCandidates()" placeholder="Search for candidates..." style="margin-bottom:10px;padding:6px;width:100%;max-width:400px;">
    <table id="candidateTable">
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Skills</th>
                <th>Job Role</th>
                <th>Applied</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Arun Prakash</td>
                <td><EMAIL></td>
                <td>Python, TensorFlow, NLP</td>
                <td>AI Engineer</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Priya Lakshmi</td>
                <td><EMAIL></td>
                <td>Data Analysis, R, SQL</td>
                <td>Data Scientist</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Karthik Raj</td>
                <td><EMAIL></td>
                <td>React, CSS, JavaScript</td>
                <td>Frontend Developer</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Divya Shree</td>
                <td><EMAIL></td>
                <td>Teaching, AI Concepts, Python</td>
                <td>AI Trainer & Educator</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Saravanan Muthu</td>
                <td><EMAIL></td>
                <td>Sales, Negotiation, CRM</td>
                <td>Business Development Manager</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Meena Ramesh</td>
                <td><EMAIL></td>
                <td>Deep Learning, PyTorch, ML Ops</td>
                <td>AI Engineer</td>
                <td>Applied</td>
            </tr>
            <tr>
                <td>Vignesh Kumar</td>
                <td><EMAIL></td>
                <td>Statistics, Data Mining, Python</td>
                <td>Data Scientist</td>
                <td>Applied</td>
            </tr>
            <tr>
                <td>Keerthana Suresh</td>
                <td><EMAIL></td>
                <td>Vue.js, HTML5, UX Design</td>
                <td>Frontend Developer</td>
                <td>Applied</td>
            </tr>
            <tr>
                <td>Senthil Kumar</td>
                <td><EMAIL></td>
                <td>Curriculum Design, AI Ethics</td>
                <td>AI Trainer & Educator</td>
                <td>Applied</td>
            </tr>
            <tr>
                <td>Revathi Balaji</td>
                <td><EMAIL></td>
                <td>Lead Generation, B2B Sales</td>
                <td>Business Development Manager</td>
                <td>Yes</td>
            </tr>
            <!-- Add more Tamil names as needed for your demo -->
        </tbody>
    </table>
    <script src="canditates.js"></script>
    <script src="canditate.js"></script>
</body>
</html>