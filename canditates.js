// Cache DOM elements outside the function for better performance
const input = document.getElementById('candidateSearch');
const table = document.getElementById('candidateTable');
const tbody = table ? table.tBodies[0] : null;

function filterCandidates() {
    if (!input || !(input instanceof HTMLInputElement)) return;
    if (!table) return;
    if (!tbody) return;

    const filter = input.value.trim().toLowerCase();

    Array.from(tbody.rows).forEach(row => {
        const match = Array.from(row.cells).some(cell =>
            (cell.textContent || '').toLowerCase().includes(filter)
        );
        row.style.display = match ? '' : 'none';
    });
}