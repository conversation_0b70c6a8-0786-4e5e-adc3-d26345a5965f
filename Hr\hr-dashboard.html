<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Dashboard - Cube AI Solutions</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="../zoho-design-system.css">
    <link rel="stylesheet" href="hr-dashboard.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    <script src="../unified-navigation.js"></script>
</head>
<body>
    <!-- Enhanced Header with Zoho Recruit Style -->
    <header class="hr-header">
        <div class="header-container">
            <div class="header-left">
                <a href="../index.html" class="logo-link">
                    <div class="logo-icon">🧊</div>
                    <div class="company-info">
                        <h1>Cube AI Solutions</h1>
                        <p>HR Dashboard</p>
                    </div>
                </a>
            </div>
            
            <nav class="header-nav">
                <ul class="nav-menu">
                    <li><a href="../index.html" class="nav-link-zoho">🏠 Home</a></li>
                    <li><a href="hr-dashboard.html" class="nav-link-zoho active">📊 Dashboard</a></li>
                    <li><a href="candidates-management.html" class="nav-link-zoho">👥 Candidates</a></li>
                    <li><a href="job-management.html" class="nav-link-zoho">💼 Jobs</a></li>
                    <li><a href="analytics.html" class="nav-link-zoho">📈 Analytics</a></li>
                </ul>
            </nav>
            
            <div class="header-actions">
                <button class="btn-zoho btn-outline btn-sm" onclick="showNotifications()">
                    <i>🔔</i> Alerts <span class="notification-badge">3</span>
                </button>
                <button class="btn-zoho btn-primary btn-sm" onclick="showProfile()">
                    <i>👤</i> HR Admin
                </button>
            </div>
        </div>
    </header>

    <!-- Main Dashboard Content -->
    <div class="hr-container">
        <!-- Dashboard Stats Cards -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>Total Candidates</h3>
                    <div class="stat-number">247</div>
                    <div class="stat-change positive">+12% this month</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">💼</div>
                <div class="stat-content">
                    <h3>Active Jobs</h3>
                    <div class="stat-number">12</div>
                    <div class="stat-change neutral">3 new this week</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📝</div>
                <div class="stat-content">
                    <h3>Applications</h3>
                    <div class="stat-number">89</div>
                    <div class="stat-change positive">+8% this week</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3>Hired</h3>
                    <div class="stat-number">15</div>
                    <div class="stat-change positive">+25% this month</div>
                </div>
            </div>
        </div>

        <!-- Dashboard Content Grid -->
        <div class="dashboard-content">
            <!-- Recent Applications -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Recent Applications</h3>
                    <button class="btn-zoho btn-outline btn-sm">View All</button>
                </div>
                <div class="applications-list">
                    <div class="application-item">
                        <div class="candidate-info">
                            <div class="candidate-avatar">AP</div>
                            <div class="candidate-details">
                                <h4>Arun Prakash</h4>
                                <p>Applied for Senior AI/ML Engineer</p>
                            </div>
                        </div>
                        <div class="application-meta">
                            <span class="application-status new">New</span>
                            <span class="application-time">2 hours ago</span>
                        </div>
                    </div>
                    
                    <div class="application-item">
                        <div class="candidate-info">
                            <div class="candidate-avatar">PL</div>
                            <div class="candidate-details">
                                <h4>Priya Lakshmi</h4>
                                <p>Applied for Data Scientist</p>
                            </div>
                        </div>
                        <div class="application-meta">
                            <span class="application-status reviewed">Reviewed</span>
                            <span class="application-time">1 day ago</span>
                        </div>
                    </div>
                    
                    <div class="application-item">
                        <div class="candidate-info">
                            <div class="candidate-avatar">KR</div>
                            <div class="candidate-details">
                                <h4>Karthik Raj</h4>
                                <p>Applied for Frontend Developer</p>
                            </div>
                        </div>
                        <div class="application-meta">
                            <span class="application-status interview">Interview</span>
                            <span class="application-time">3 days ago</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Quick Actions</h3>
                </div>
                <div class="quick-actions">
                    <button class="action-btn" onclick="createJob()">
                        <div class="action-icon">➕</div>
                        <div class="action-text">
                            <h4>Post New Job</h4>
                            <p>Create a new job posting</p>
                        </div>
                    </button>
                    
                    <button class="action-btn" onclick="reviewCandidates()">
                        <div class="action-icon">👀</div>
                        <div class="action-text">
                            <h4>Review Candidates</h4>
                            <p>View pending applications</p>
                        </div>
                    </button>
                    
                    <button class="action-btn" onclick="scheduleInterview()">
                        <div class="action-icon">📅</div>
                        <div class="action-text">
                            <h4>Schedule Interview</h4>
                            <p>Set up candidate interviews</p>
                        </div>
                    </button>
                    
                    <button class="action-btn" onclick="generateReport()">
                        <div class="action-icon">📊</div>
                        <div class="action-text">
                            <h4>Generate Report</h4>
                            <p>Create hiring analytics</p>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Hiring Pipeline -->
            <div class="dashboard-card pipeline-card">
                <div class="card-header">
                    <h3>Hiring Pipeline</h3>
                    <select class="pipeline-filter">
                        <option>All Positions</option>
                        <option>AI/ML Engineer</option>
                        <option>Frontend Developer</option>
                        <option>Data Scientist</option>
                    </select>
                </div>
                <div class="pipeline-stages">
                    <div class="pipeline-stage">
                        <h4>Applied</h4>
                        <div class="stage-count">89</div>
                        <div class="stage-bar">
                            <div class="stage-progress" style="width: 100%"></div>
                        </div>
                    </div>
                    
                    <div class="pipeline-stage">
                        <h4>Screening</h4>
                        <div class="stage-count">34</div>
                        <div class="stage-bar">
                            <div class="stage-progress" style="width: 38%"></div>
                        </div>
                    </div>
                    
                    <div class="pipeline-stage">
                        <h4>Interview</h4>
                        <div class="stage-count">18</div>
                        <div class="stage-bar">
                            <div class="stage-progress" style="width: 20%"></div>
                        </div>
                    </div>
                    
                    <div class="pipeline-stage">
                        <h4>Offer</h4>
                        <div class="stage-count">8</div>
                        <div class="stage-bar">
                            <div class="stage-progress" style="width: 9%"></div>
                        </div>
                    </div>
                    
                    <div class="pipeline-stage">
                        <h4>Hired</h4>
                        <div class="stage-count">5</div>
                        <div class="stage-bar">
                            <div class="stage-progress" style="width: 6%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Recent Activity</h3>
                </div>
                <div class="activity-feed">
                    <div class="activity-item">
                        <div class="activity-icon">👤</div>
                        <div class="activity-content">
                            <p><strong>Arun Prakash</strong> applied for Senior AI/ML Engineer</p>
                            <span class="activity-time">2 hours ago</span>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">💼</div>
                        <div class="activity-content">
                            <p>New job <strong>IoT Solutions Architect</strong> was posted</p>
                            <span class="activity-time">4 hours ago</span>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">✅</div>
                        <div class="activity-content">
                            <p><strong>Priya Lakshmi</strong> was moved to interview stage</p>
                            <span class="activity-time">1 day ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="hr-dashboard.js"></script>
</body>
</html>
