/* Cube AI Solutions - Recruitment Portal Styles */
/* Complete Zoho Recruit C<PERSON> Styling - FIXED VERSION */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* ===== CSS VARIABLES ===== */
:root {
    /* Primary Brand Colors */
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --primary-blue-light: #3b82f6;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* Status Colors */
    --success-green: #10b981;
    --warning-orange: #f59e0b;
    --danger-red: #ef4444;
    --info-cyan: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Layout Dimensions */
    --header-height: 64px;
    --nav-height: 52px;
    --sidebar-width: 280px;
    
    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Z-index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
    --z-toast: 1070;
}

/* ===== GLOBAL RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden; /* Fix horizontal scroll issues */
}

/* ===== LOADING SCREEN ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-base);
}

.loading-content {
    text-align: center;
    padding: var(--space-8);
}

.loading-spinner {
    margin-bottom: var(--space-6);
}

.loading-text h4 {
    color: var(--primary-blue);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.loading-text p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

.loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

/* ===== LOGIN PAGE ===== */
.login-container {
    min-height: 100vh;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
}

/* FIXED: Properly encoded SVG pattern */
.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grid' width='10' height='10' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 10 0 L 0 0 0 10' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.login-wrapper {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--space-16);
    max-width: 1200px;
    width: 100%;
    align-items: center;
    position: relative;
    z-index: 1;
}

/* Login Branding */
.login-brand {
    color: var(--white);
    padding: var(--space-8);
}

.brand-content {
    text-align: center;
    margin-bottom: var(--space-12);
}

.company-logo {
    font-size: 4rem;
    margin-bottom: var(--space-6);
    animation: float 3s ease-in-out infinite;
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

@keyframes float {
    0%, 100% { 
        transform: translate3d(0, 0, 0); 
    }
    50% { 
        transform: translate3d(0, -10px, 0); 
    }
}

.company-name {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.company-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--space-6);
}

.brand-divider {
    width: 60px;
    height: 3px;
    background: var(--white);
    margin: 0 auto var(--space-6);
    border-radius: var(--radius-lg);
}

.portal-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.portal-description {
    font-size: var(--font-size-base);
    opacity: 0.8;
}

/* Features Showcase */
.features-showcase h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-8);
    text-align: center;
}

.feature-grid {
    display: grid;
    gap: var(--space-6);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
    padding: var(--space-6);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    /* FIXED: Added vendor prefixes for backdrop-filter */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-base);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translate3d(0, -2px, 0);
}

.feature-item i {
    font-size: 1.5rem;
    margin-top: var(--space-1);
    opacity: 0.9;
}

.feature-item strong {
    display: block;
    font-weight: 600;
    margin-bottom: var(--space-1);
    font-size: var(--font-size-base);
}

.feature-item p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    line-height: 1.5;
    margin: 0;
}

/* Login Form Container */
.login-form-container {
    position: relative;
}

.login-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    padding: var(--space-8) var(--space-8) var(--space-6);
    text-align: center;
    background: linear-gradient(180deg, var(--gray-50) 0%, var(--white) 100%);
    border-bottom: 1px solid var(--gray-100);
}

.login-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--space-2);
}

.login-header p {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Login Form */
.login-form {
    padding: var(--space-8);
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-3);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
    background: var(--white);
    font-family: inherit;
    /* FIXED: Added proper box-sizing */
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:valid {
    border-color: var(--success-green);
}

.form-control.is-invalid {
    border-color: var(--danger-red);
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    /* FIXED: Added z-index to ensure it's clickable */
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: var(--gray-50);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    /* FIXED: Added flex-wrap for better responsive behavior */
    flex-wrap: wrap;
    gap: var(--space-3);
}

.remember-section {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.form-check-input {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-blue);
}

.form-check-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    cursor: pointer;
}

.forgot-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition-fast);
}

.forgot-link:hover {
    text-decoration: underline;
    color: var(--primary-blue-dark);
}

/* Login Button */
.btn-login {
    width: 100%;
    padding: 14px 24px;
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    margin-bottom: var(--space-6);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

.btn-login:hover:not(:disabled) {
    background: var(--primary-blue-dark);
    transform: translate3d(0, -1px, 0);
    box-shadow: var(--shadow-lg);
}

.btn-login:active {
    transform: translate3d(0, 0, 0);
}

.btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: translate3d(0, 0, 0);
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Demo Credentials */
.demo-credentials {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-6);
}

.demo-title {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: var(--space-2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.demo-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.demo-info span {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-family: 'Courier New', monospace;
}

/* Login Footer */
.login-footer {
    padding: var(--space-6) var(--space-8);
    background: var(--gray-50);
    text-align: center;
    border-top: 1px solid var(--gray-100);
}

.login-footer p {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--space-3);
}

.login-footer a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
}

.login-footer a:hover {
    text-decoration: underline;
}

.powered-by small {
    color: var(--gray-400);
    font-size: var(--font-size-xs);
}

/* Form Validation */
.invalid-feedback {
    display: none;
    color: var(--danger-red);
    font-size: var(--font-size-xs);
    margin-top: var(--space-2);
}

.form-control.is-invalid + .invalid-feedback {
    display: block;
}

/* ===== MAIN APPLICATION ===== */
.main-application {
    min-height: 100vh;
    background: var(--gray-50);
}

/* Top Header */
.top-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-sm);
}

.header-content {
    height: 100%;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.app-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 700;
    font-size: var(--font-size-lg);
    color: var(--primary-blue);
}

.app-brand i {
    font-size: 1.75rem;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* Header Center - Search */
.header-center {
    flex: 1;
    max-width: 600px;
    margin: 0 var(--space-8);
}

.search-container {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.search-input-group {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 1;
}

.global-search {
    width: 100%;
    padding: 10px 16px 10px 40px;
    border: 1px solid var(--gray-200);
    border-radius: 25px;
    font-size: var(--font-size-sm);
    background: var(--gray-50);
    transition: var(--transition-base);
}

.global-search:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: var(--white);
    box-shadow: var(--shadow-sm);
}

.search-filter-btn {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 10px 12px;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-filter-btn:hover {
    background: var(--gray-50);
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

/* Header Right */
.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.quick-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

.action-btn:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
    transform: translate3d(0, -1px, 0);
}

/* Notifications */
.notification-btn {
    position: relative;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 10px 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.notification-btn:hover {
    background: var(--gray-50);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--danger-red);
    color: var(--white);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid var(--white);
}

.notification-dropdown {
    width: 380px;
    border: none;
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-xl);
    padding: 0;
    margin-top: 8px;
    /* FIXED: Added max-width for small screens */
    max-width: calc(100vw - 2rem);
}

.notification-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h6 {
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.mark-read-btn {
    background: none;
    border: none;
    color: var(--primary-blue);
    font-size: var(--font-size-xs);
    cursor: pointer;
    font-weight: 500;
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-footer {
    padding: var(--space-4) var(--space-6);
    text-align: center;
    border-top: 1px solid var(--gray-100);
}

.view-all-notifications {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* User Menu */
.user-btn {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 6px 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.user-btn:hover {
    background: var(--gray-50);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    line-height: 1.2;
}

.user-role {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.2;
}

.user-dropdown {
    width: 280px;
    border: none;
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-xl);
    padding: 0;
    margin-top: 8px;
    /* FIXED: Added max-width for small screens */
    max-width: calc(100vw - 2rem);
}

.user-profile-section {
    padding: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.user-avatar-large {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.user-details strong {
    display: block;
    color: var(--gray-800);
    font-weight: 600;
    margin-bottom: var(--space-1);
}

.user-details p {
    margin: 0 0 var(--space-2);
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

.user-status {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
}

.user-status.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.user-status.online::before {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--success-green);
    border-radius: 50%;
}

/* Main Navigation */
.main-navigation {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    height: var(--nav-height);
    position: sticky;
    top: var(--header-height);
    z-index: calc(var(--z-sticky) - 1);
}

.nav-container {
    height: 100%;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-tabs-wrapper {
    display: flex;
    height: 100%;
    gap: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-tabs-wrapper::-webkit-scrollbar {
    display: none;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: 0 var(--space-5);
    height: 100%;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    position: relative;
    /* FIXED: Ensure minimum width for touch targets */
    min-width: 44px;
}

.nav-tab:hover {
    background: var(--gray-50);
    color: var(--primary-blue);
}

.nav-tab.active {
    color: var(--primary-blue);
    border-bottom-color: var(--primary-blue);
    background: var(--gray-50);
}

.nav-tab i {
    font-size: 1rem;
}

.tab-badge {
    background: var(--primary-blue);
    color: var(--white);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.nav-actions {
    display: flex;
    gap: var(--space-2);
}

.nav-action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 8px 12px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
}

.nav-action-btn:hover {
    background: var(--gray-50);
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.nav-action-btn.primary {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.nav-action-btn.primary:hover {
    background: var(--primary-blue-dark);
}

/* Main Content */
.main-content {
    padding: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - var(--header-height) - var(--nav-height));
}

.module-content {
    display: none;
}

.module-content.active {
    display: block;
    animation: fadeSlideIn 0.4s ease-out;
}

@keyframes fadeSlideIn {
    from {
        opacity: 0;
        transform: translate3d(0, 20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

/* Module Header */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-8);
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    /* FIXED: Added flex-wrap for better responsive behavior */
    flex-wrap: wrap;
    gap: var(--space-4);
}

.header-info h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
}

.header-info p {
    color: var(--gray-500);
    font-size: var(--font-size-base);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: var(--space-3);
    /* FIXED: Added flex-wrap for better responsive behavior */
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: 10px 20px;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-base);
    text-decoration: none;
    border: 1px solid;
    background: var(--white);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
    /* FIXED: Ensure minimum size for touch targets */
    min-height: 44px;
}

.btn:hover {
    transform: translate3d(0, -1px, 0);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background: var(--primary-blue-dark);
    border-color: var(--primary-blue-dark);
    color: var(--white);
}

.btn-outline-primary {
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline-primary:hover {
    background: var(--primary-blue);
    color: var(--white);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

/* Widget Base */
.widget {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    overflow: hidden;
    transition: var(--transition-base);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

.widget:hover {
    box-shadow: var(--shadow-lg);
    transform: translate3d(0, -2px, 0);
}

.widget-header {
    padding: var(--space-6) var(--space-6) var(--space-4;
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
}

.widget-content {
    padding: var(--space-6);
}

.view-all-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.view-all-link:hover {
    text-decoration: underline;
}

/* Stats Widget */
.stats-widget {
    grid-column: 1 / -1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-100);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
}

.stat-card.primary {
    --card-color: var(--primary-blue);
}

.stat-card.success {
    --card-color: var(--success-green);
}

.stat-card.warning {
    --card-color: var(--warning-orange);
}

.stat-card.info {
    --card-color: var(--info-cyan);
}

.stat-card:hover {
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    background: var(--card-color);
}

.stat-details h4 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-800);
    margin-bottom: var(--space-1);
    line-height: 1;
}

.stat-label {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-2);
    font-weight: 500;
}

.stat-trend {
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.stat-trend.positive {
    color: var(--success-green);
}

.stat-trend.negative {
    color: var(--danger-red);
}

/* Activity Timeline */
.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-100);
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
    box-shadow: var(--shadow-sm);
}

.activity-avatar {
    position: relative;
    /* FIXED: Added flex-shrink to prevent avatar compression */
    flex-shrink: 0;
}

.activity-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--white);
}

.status-indicator.online {
    background: var(--success-green);
}

.activity-info {
    flex: 1;
    /* FIXED: Added min-width to prevent text compression */
    min-width: 0;
}

.activity-info h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-1);
    /* FIXED: Added text overflow handling */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.activity-info p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--space-2);
    /* FIXED: Added line clamping for long text */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.activity-action {
    display: flex;
    gap: var(--space-2);
    /* FIXED: Added flex-shrink to prevent button compression */
    flex-shrink: 0;
}

.activity-action button {
    padding: 6px 12px;
    font-size: var(--font-size-xs);
    border-radius: var(--radius-md);
}

/* Pipeline Chart */
.pipeline-chart {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.pipeline-stage {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.stage-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stage-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

.stage-count {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-blue);
    background: rgba(37, 99, 235, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 24px;
    text-align: center;
}

.stage-bar {
    height: 8px;
    background: var(--gray-100);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-blue);
    border-radius: 4px;
    transition: width 0.8s ease-out;
    width: 0%;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--space-4);
}

.quick-action-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-6);
    border: 1px solid var(--gray-100);
    border-radius: var(--radius-xl);
    background: var(--white);
    cursor: pointer;
    transition: var(--transition-base);
    text-decoration: none;
    color: var(--gray-700);
    /* FIXED: Added GPU acceleration */
    transform: translate3d(0, 0, 0);
    /* FIXED: Ensure minimum touch target size */
    min-height: 120px;
}

.quick-action-card:hover {
    background: var(--gray-50);
    border-color: var(--primary-blue);
    transform: translate3d(0, -2px, 0);
    box-shadow: var(--shadow-md);
    color: var(--gray-800);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.action-icon.primary { background: var(--primary-blue); }
.action-icon.success { background: var(--success-green); }
.action-icon.warning { background: var(--warning-orange); }
.action-icon.info { background: var(--info-cyan); }
.action-icon.secondary { background: var(--gray-500); }
.action-icon.dark { background: var(--gray-700); }

.quick-action-card span {
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: center;
    /* FIXED: Added line height for better text spacing */
    line-height: 1.4;
}

/* Performance Widget */
.performer-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
}

.performer-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-100);
    transition: var(--transition-fast);
}

.performer-item:hover {
    background: var(--gray-50);
    box-shadow: var(--shadow-sm);
}

.performer-avatar {
    position: relative;
    /* FIXED: Added flex-shrink to prevent avatar compression */
    flex-shrink: 0;
}

.performer-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.performer-info {
    flex: 1;
    /* FIXED: Added min-width to prevent text compression */
    min-width: 0;
}

.performer-info h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-1);
    /* FIXED: Added text overflow handling */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.performer-info p {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--space-2);
}

.performance-stats {
    display: flex;
    gap: var(--space-4);
    /* FIXED: Added flex-wrap for better responsive behavior */
    flex-wrap: wrap;
}

.performance-stats .stat {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.performance-stats .stat i {
    color: var(--primary-blue);
}

.performer-score {
    display: flex;
    align-items: center;
    /* FIXED: Added flex-shrink to prevent score compression */
    flex-shrink: 0;
}

.score-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-blue);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-lg);
}

/* Coming Soon Placeholder */
.coming-soon {
    text-align: center;
    padding: var(--space-16) var(--space-8);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
}

.coming-soon i {
    color: var(--gray-300);
    margin-bottom: var(--space-8);
}

.coming-soon h3 {
    color: var(--gray-600);
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.coming-soon p {
    color: var(--gray-500);
    font-size: var(--font-size-base);
    margin-bottom: var(--space-8);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Dropdown Menus */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-lg);
    padding: var(--space-2) 0;
    margin-top: var(--space-2);
    background: var(--white);
    min-width: 200px;
    /* FIXED: Added max-width for small screens */
    max-width: calc(100vw - 2rem);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px var(--space-4);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    /* FIXED: Ensure minimum touch target size */
    min-height: 40px;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

.dropdown-item.text-danger {
    color: var(--danger-red);
}

.dropdown-item.text-danger:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-red);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-100);
    margin: var(--space-2) 0;
    border: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .login-wrapper {
        grid-template-columns: 1fr;
        gap: var(--space-8);
        max-width: 500px;
    }
    
    .header-content {
        padding: 0 var(--space-4);
    }
    
    .nav-container {
        padding: 0 var(--space-4);
    }
    
    .main-content {
        padding: var(--space-6);
    }
    
    .module-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-4);
    }
    
    .header-actions {
        justify-content: flex-end;
    }
}

@media (max-width: 768px) {
    .header-center {
        display: none;
    }
    
    .quick-actions {
        display: none;
    }
    
    .user-info {
        display: none;
    }
    
    .nav-tab span {
        display: none;
    }
    
    .nav-tab {
        padding: 0 var(--space-3);
        min-width: 44px;
        justify-content: center;
    }
    
    .nav-actions {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .feature-grid {
        gap: var(--space-4);
    }
    
    .feature-item {
        padding: var(--space-4);
    }
    
    /* FIXED: Better responsive behavior for performance stats */
    .performance-stats {
        gap: var(--space-2);
    }
    
    /* FIXED: Better responsive behavior for form options */
    .form-options {
        flex-direction: column;
        align-items: stretch;
    }
}

@media (max-width: 576px) {
    .login-container {
        padding: var(--space-4);
    }
    
    .header-content {
        padding: 0 var(--space-2);
    }
    
    .nav-container {
        padding: 0 var(--space-2);
    }
    
    .main-content {
        padding: var(--space-4);
    }
    
    .module-header {
        padding: var(--space-6);
    }
    
    .widget-content {
        padding: var(--space-4);
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        gap: var(--space-4);
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }
    
    .notification-dropdown,
    .user-dropdown {
        width: calc(100vw - 2rem);
        max-width: 320px;
    }
    
    /* FIXED: Better responsive behavior for company name */
    .company-name {
        font-size: var(--font-size-3xl);
    }
    
    /* FIXED: Better responsive behavior for header info */
    .header-info h1 {
        font-size: var(--font-size-2xl);
    }
}

/* FIXED: Added media query for very small screens */
@media (max-width: 320px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .company-name {
        font-size: var(--font-size-2xl);
    }
    
    .header-info h1 {
        font-size: var(--font-size-xl);
    }
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.fw-normal { font-weight: 400 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-bold { font-weight: 700 !important; }

.text-primary { color: var(--primary-blue) !important; }
.text-success { color: var(--success-green) !important; }
.text-warning { color: var(--warning-orange) !important; }
.text-danger { color: var(--danger-red) !important; }
.text-info { color: var(--info-cyan) !important; }
.text-muted { color: var(--gray-500) !important; }

.bg-primary { background-color: var(--primary-blue) !important; }
.bg-success { background-color: var(--success-green) !important; }
.bg-warning { background-color: var(--warning-orange) !important; }
.bg-danger { background-color: var(--danger-red) !important; }
.bg-info { background-color: var(--info-cyan) !important; }

/* FIXED: Added accessibility utilities */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* FIXED: Added focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.nav-tab:focus,
.action-btn:focus,
.dropdown-item:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* FIXED: Improved reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Print Styles */
@media print {
    .top-header,
    .main-navigation,
    .header-actions,
    .nav-actions {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .widget {
        box-shadow: none;
        border: 1px solid var(--gray-300);
        break-inside: avoid;
        margin-bottom: var(--space-6);
    }
    
    body {
        background: var(--white) !important;
    }
}