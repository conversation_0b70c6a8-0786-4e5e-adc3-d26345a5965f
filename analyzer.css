/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #4a90e2;
    text-decoration: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #4a90e2;
}

/* Main Content */
main {
    margin-top: 80px;
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    color: white;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Application Section */
.app-section {
    background: white;
    border-radius: 20px;
    margin: 2rem 0;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.app-header {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    padding: 2rem;
    text-align: center;
}

.app-content {
    padding: 3rem;
}

/* Upload Section */
.upload-section {
    text-align: center;
    margin-bottom: 3rem;
}

.upload-area {
    border: 3px dashed #4a90e2;
    border-radius: 15px;
    padding: 3rem;
    margin: 2rem 0;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #357abd;
    background: #f0f4ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #28a745;
    background: #f0fff4;
}

.upload-icon {
    font-size: 4rem;
    color: #4a90e2;
    margin-bottom: 1rem;
}

.file-input {
    display: none;
}

.upload-btn {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

/* Analysis Section */
.analysis-section {
    display: none;
    margin-top: 3rem;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.analysis-card {
    background: #f8f9ff;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-5px);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 2rem;
    margin-right: 1rem;
    color: #4a90e2;
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin: 1rem auto;
}

.score-excellent { 
    background: linear-gradient(135deg, #28a745, #20c997); 
}

.score-good { 
    background: linear-gradient(135deg, #ffc107, #fd7e14); 
}

.score-average { 
    background: linear-gradient(135deg, #fd7e14, #dc3545); 
}

.suggestions-list {
    list-style: none;
    margin-top: 1rem;
}

.suggestions-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.suggestions-list li:last-child {
    border-bottom: none;
}

.suggestions-list i {
    color: #4a90e2;
    margin-right: 0.5rem;
}

/* Skills Section */
.skills-chart {
    margin-top: 1rem;
}

.skill-item {
    margin-bottom: 1rem;
}

.skill-name {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.skill-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, #4a90e2, #357abd);
    border-radius: 4px;
    transition: width 1s ease;
}

/* Loading Animation */
.loading {
    text-align: center;
    padding: 3rem;
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4a90e2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Job Matching Section */
.job-match {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    text-align: center;
}

.match-percentage {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.job-recommendations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.job-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.job-card:hover {
    transform: scale(1.05);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .nav-links {
        display: none;
    }

    .app-content {
        padding: 2rem;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .upload-area {
        padding: 2rem;
    }

    .upload-icon {
        font-size: 3rem;
    }
}

/* Additional Mobile Styles */
@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .app-content {
        padding: 1.5rem;
    }

    .upload-area {
        padding: 1.5rem;
    }

    .analysis-card {
        padding: 1.5rem;
    }

    .job-recommendations {
        grid-template-columns: 1fr;
    }
}
