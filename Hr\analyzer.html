<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Analyzer | CubeAI Solutions</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="analyzer.css">
</head>
<body>
    <header>
        <nav class="container">
            <a href="#" class="logo">
                <i class="fas fa-cube"></i> CubeAI Resume Analyzer
            </a>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#analyzer">Analyzer</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h1>AI-Powered Resume Analyzer</h1>
                <p>Get instant insights, improve your resume, and land your dream job with our advanced AI technology</p>
            </div>
        </section>

        <div class="container">
            <div class="app-section">
                <div class="app-header">
                    <h2><i class="fas fa-robot"></i> Intelligent Resume Analysis</h2>
                    <p>Upload your resume and let our AI provide comprehensive feedback and improvement suggestions</p>
                </div>

                <div class="app-content">
                    <!-- Job Description Section -->
                    <div class="job-description-section" style="margin-bottom:2rem;">
                        <label for="jobDescription" style="font-weight:bold;">Job Description (Paste or type the requirements):</label>
                        <textarea id="jobDescription" rows="5" style="width:100%;margin-top:0.5rem;padding:1rem;border-radius:8px;border:1px solid #ccc;resize:vertical;" placeholder="Enter the job description or requirements here..."></textarea>
                    </div>

                    <!-- Upload Section -->
                    <div class="upload-section">
                        <div class="upload-area" id="uploadArea" tabindex="0" role="button" aria-label="Upload your CVs here. Press Enter to open file dialog.">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h3>Drop your CVs here or click to browse</h3>
                            <p>Supports PDF, DOC, DOCX files (Max 5MB each, multiple files allowed)</p>
                            <input type="file" id="fileInput" class="file-input" accept=".pdf,.doc,.docx" multiple>
                            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-upload"></i> Analyse Now
                            </button>
                        </div>
                        <div id="fileName" style="margin-top: 1rem; font-weight: bold; color: #4a90e2;"></div>
                    </div>

                    <!-- Loading Section -->
                    <div class="loading" id="loadingSection" style="display:none;" aria-live="polite" aria-atomic="true">
                        <div class="spinner" role="status" aria-label="Loading"></div>
                        <h3>Analyzing your resume...</h3>
                        <p>Our AI is processing your document and generating insights</p>
                    </div>

                    <!-- Analysis Results -->
                    <div class="analysis-section" id="analysisSection" style="display:none;">
                        <h2><i class="fas fa-chart-line"></i> Analysis Results</h2>
                        
                        <div class="analysis-grid">
                            <!-- Overall Score -->
                            <div class="analysis-card">
                                <div class="card-header">
                                    <div class="card-icon">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <h3>Overall Score</h3>
                                </div>
                                <div class="score-circle score-good" id="overallScore">
                                    78%
                                </div>
                                <p>Your resume shows good potential with room for improvement in key areas.</p>
                            </div>

                            <!-- ATS Compatibility -->
                            <div class="analysis-card">
                                <div class="card-header">
                                    <div class="card-icon">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <h3>ATS Compatibility</h3>
                                </div>
                                <div class="score-circle score-excellent" id="atsScore">
                                    92%
                                </div>
                                <p>Excellent! Your resume is well-optimized for Applicant Tracking Systems.</p>
                            </div>

                            <!-- Skills Analysis -->
                            <div class="analysis-card">
                                <div class="card-header">
                                    <div class="card-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <h3>Skills Match</h3>
                                </div>
                                <div class="skills-chart">
                                    <div class="skill-item">
                                        <div class="skill-name">
                                            <span>JavaScript</span>
                                            <span>90%</span>
                                        </div>
                                        <div class="skill-bar">
                                            <div class="skill-progress" style="width: 90%"></div>
                                        </div>
                                    </div>
                                    <div class="skill-item">
                                        <div class="skill-name">
                                            <span>Python</span>
                                            <span>85%</span>
                                        </div>
                                        <div class="skill-bar">
                                            <div class="skill-progress" style="width: 85%"></div>
                                        </div>
                                    </div>
                                    <div class="skill-item">
                                        <div class="skill-name">
                                            <span>React</span>
                                            <span>80%</span>
                                        </div>
                                        <div class="skill-bar">
                                            <div class="skill-progress" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Suggestions -->
                            <div class="analysis-card">
                                <div class="card-header">
                                    <div class="card-icon">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <h3>Improvement Suggestions</h3>
                                </div>
                                <ul class="suggestions-list">
                                    <li><i class="fas fa-check"></i> Add more quantifiable achievements</li>
                                    <li><i class="fas fa-check"></i> Include relevant keywords for your target role</li>
                                    <li><i class="fas fa-check"></i> Optimize your professional summary</li>
                                    <li><i class="fas fa-check"></i> Add technical certifications</li>
                                    <li><i class="fas fa-check"></i> Include project portfolio links</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Job Matching -->
                        <div class="job-match">
                            <h3><i class="fas fa-bullseye"></i> Job Match Analysis</h3>
                            <div class="match-percentage">
                                <span id="jobMatchScore">87%</span>
                            </div>
                            <p>Match for Software Developer positions</p>
                            
                            <div class="job-recommendations">
                                <div class="job-card">
                                    <h4>Frontend Developer</h4>
                                    <p>Match: 92%</p>
                                    <small>Based on React, JavaScript skills</small>
                                </div>
                                <div class="job-card">
                                    <h4>Full Stack Developer</h4>
                                    <p>Match: 85%</p>
                                    <small>Based on diverse tech stack</small>
                                </div>
                                <div class="job-card">
                                    <h4>UI/UX Developer</h4>
                                    <p>Match: 78%</p>
                                    <small>Frontend skills alignment</small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="downloadReport()">
                                <i class="fas fa-download"></i> Download Report
                            </button>
                            <button class="btn btn-secondary" onclick="analyzeAnother()">
                                <i class="fas fa-redo"></i> Analyze Another
                            </button>
                            <a href="#jobs" class="btn btn-primary">
                                <i class="fas fa-briefcase"></i> Browse Jobs
                            </a>
                        </div>
                    </div>

                    <!-- ATS Results Section -->
                    <div class="ats-results-section" id="atsResultsSection" style="display:none; margin-top:2rem;">
                        <h3><i class="fas fa-list"></i> ATS Scores for Uploaded CVs</h3>
                        <table style="width:100%;border-collapse:collapse;margin-top:1rem;">
                            <thead>
                                <tr style="background:#f8f9ff;">
                                    <th style="padding:0.5rem 1rem;text-align:left;">File Name</th>
                                    <th style="padding:0.5rem 1rem;text-align:left;">ATS Score</th>
                                </tr>
                            </thead>
                            <tbody id="atsResultsTable">
                                <!-- JS will populate this -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="analyzer.js"></script>
</body>
</html>