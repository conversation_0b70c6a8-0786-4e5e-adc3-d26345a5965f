// HR Dashboard JavaScript - Zoho Recruit Style

// Global Variables
let dashboardData = {
    stats: {
        totalCandidates: 247,
        activeJobs: 12,
        applications: 89,
        hired: 15
    },
    recentApplications: [],
    activities: [],
    pipelineData: {}
};

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    loadDashboardData();
    setupEventListeners();
    startRealTimeUpdates();
});

function initializeDashboard() {
    console.log('🚀 HR Dashboard initialized');
    
    // Add loading states
    showLoadingStates();
    
    // Simulate data loading
    setTimeout(() => {
        hideLoadingStates();
        animateStats();
    }, 1500);
}

function loadDashboardData() {
    // Simulate API calls to load dashboard data
    loadRecentApplications();
    loadActivityFeed();
    loadPipelineData();
}

function loadRecentApplications() {
    // Simulate recent applications data
    const applications = [
        {
            id: 1,
            candidateName: '<PERSON><PERSON> <PERSON>',
            position: 'Senior AI/ML Engineer',
            status: 'new',
            appliedTime: '2 hours ago',
            avatar: 'AP'
        },
        {
            id: 2,
            candidateName: '<PERSON><PERSON> <PERSON>',
            position: 'Data Scientist',
            status: 'reviewed',
            appliedTime: '1 day ago',
            avatar: 'PL'
        },
        {
            id: 3,
            candidateName: 'Karthik Raj',
            position: 'Frontend Developer',
            status: 'interview',
            appliedTime: '3 days ago',
            avatar: 'KR'
        }
    ];
    
    dashboardData.recentApplications = applications;
}

function loadActivityFeed() {
    // Simulate activity feed data
    const activities = [
        {
            id: 1,
            type: 'application',
            message: '<strong>Arun Prakash</strong> applied for Senior AI/ML Engineer',
            time: '2 hours ago',
            icon: '👤'
        },
        {
            id: 2,
            type: 'job_posted',
            message: 'New job <strong>IoT Solutions Architect</strong> was posted',
            time: '4 hours ago',
            icon: '💼'
        },
        {
            id: 3,
            type: 'status_change',
            message: '<strong>Priya Lakshmi</strong> was moved to interview stage',
            time: '1 day ago',
            icon: '✅'
        }
    ];
    
    dashboardData.activities = activities;
}

function loadPipelineData() {
    // Simulate pipeline data
    dashboardData.pipelineData = {
        applied: 89,
        screening: 34,
        interview: 18,
        offer: 8,
        hired: 5
    };
}

function setupEventListeners() {
    // Pipeline filter change
    const pipelineFilter = document.querySelector('.pipeline-filter');
    if (pipelineFilter) {
        pipelineFilter.addEventListener('change', function() {
            updatePipelineData(this.value);
        });
    }
    
    // Application items click
    document.querySelectorAll('.application-item').forEach(item => {
        item.addEventListener('click', function() {
            const candidateName = this.querySelector('.candidate-details h4').textContent;
            viewCandidateDetails(candidateName);
        });
    });
    
    // Activity items click
    document.querySelectorAll('.activity-item').forEach(item => {
        item.addEventListener('click', function() {
            // Handle activity item click
            console.log('Activity clicked:', this);
        });
    });
}

function animateStats() {
    // Animate stat numbers
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 30;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });
}

function showLoadingStates() {
    // Add loading spinners to cards
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
        const content = card.querySelector('.applications-list, .quick-actions, .pipeline-stages, .activity-feed');
        if (content) {
            content.style.opacity = '0.5';
        }
    });
}

function hideLoadingStates() {
    // Remove loading states
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
        const content = card.querySelector('.applications-list, .quick-actions, .pipeline-stages, .activity-feed');
        if (content) {
            content.style.opacity = '1';
        }
    });
}

function startRealTimeUpdates() {
    // Simulate real-time updates every 30 seconds
    setInterval(() => {
        updateNotificationBadge();
        checkForNewApplications();
    }, 30000);
}

function updateNotificationBadge() {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        const currentCount = parseInt(badge.textContent);
        // Randomly update notification count
        if (Math.random() > 0.7) {
            badge.textContent = currentCount + 1;
            showNotificationToast('New application received!');
        }
    }
}

function checkForNewApplications() {
    // Simulate checking for new applications
    if (Math.random() > 0.8) {
        addNewApplication({
            candidateName: 'New Candidate',
            position: 'Software Engineer',
            status: 'new',
            appliedTime: 'Just now',
            avatar: 'NC'
        });
    }
}

function addNewApplication(application) {
    const applicationsList = document.querySelector('.applications-list');
    if (applicationsList) {
        const newItem = createApplicationItem(application);
        applicationsList.insertBefore(newItem, applicationsList.firstChild);
        
        // Animate new item
        newItem.style.opacity = '0';
        newItem.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            newItem.style.transition = 'all 0.3s ease';
            newItem.style.opacity = '1';
            newItem.style.transform = 'translateY(0)';
        }, 100);
    }
}

function createApplicationItem(application) {
    const item = document.createElement('div');
    item.className = 'application-item';
    item.innerHTML = `
        <div class="candidate-info">
            <div class="candidate-avatar">${application.avatar}</div>
            <div class="candidate-details">
                <h4>${application.candidateName}</h4>
                <p>Applied for ${application.position}</p>
            </div>
        </div>
        <div class="application-meta">
            <span class="application-status ${application.status}">${application.status}</span>
            <span class="application-time">${application.appliedTime}</span>
        </div>
    `;
    
    item.addEventListener('click', function() {
        viewCandidateDetails(application.candidateName);
    });
    
    return item;
}

// Quick Action Functions
function createJob() {
    showNotificationToast('Redirecting to job creation form...');
    // Simulate navigation to job creation
    setTimeout(() => {
        window.location.href = 'job-management.html?action=create';
    }, 1000);
}

function reviewCandidates() {
    showNotificationToast('Loading candidate review panel...');
    setTimeout(() => {
        window.location.href = 'candidates-management.html';
    }, 1000);
}

function scheduleInterview() {
    showNotificationToast('Opening interview scheduler...');
    // Simulate opening interview scheduler
    setTimeout(() => {
        alert('Interview scheduler would open here');
    }, 1000);
}

function generateReport() {
    showNotificationToast('Generating hiring analytics report...');
    setTimeout(() => {
        window.location.href = 'analytics.html';
    }, 1000);
}

// Utility Functions
function viewCandidateDetails(candidateName) {
    showNotificationToast(`Loading details for ${candidateName}...`);
    setTimeout(() => {
        window.location.href = `candidate-profile.html?name=${encodeURIComponent(candidateName)}`;
    }, 1000);
}

function updatePipelineData(filter) {
    showNotificationToast(`Updating pipeline for: ${filter}`);
    
    // Simulate updating pipeline data based on filter
    const stages = document.querySelectorAll('.pipeline-stage');
    stages.forEach(stage => {
        const count = stage.querySelector('.stage-count');
        const progress = stage.querySelector('.stage-progress');
        
        // Simulate new data
        const newCount = Math.floor(Math.random() * 50) + 10;
        count.textContent = newCount;
        
        // Update progress bar
        const percentage = (newCount / 100) * 100;
        progress.style.width = `${Math.min(percentage, 100)}%`;
    });
}

function showNotifications() {
    showNotificationToast('Notifications panel opened');
    // Simulate notifications panel
    alert('Notifications:\n• 3 new applications\n• 2 interviews scheduled\n• 1 offer accepted');
}

function showProfile() {
    showNotificationToast('Profile menu opened');
    // Simulate profile menu
    alert('Profile Menu:\n• Account Settings\n• Preferences\n• Logout');
}

function showNotificationToast(message) {
    // Create and show toast notification
    const toast = document.createElement('div');
    toast.className = 'notification-toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #2c3e50;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Export functions for global access
window.createJob = createJob;
window.reviewCandidates = reviewCandidates;
window.scheduleInterview = scheduleInterview;
window.generateReport = generateReport;
window.showNotifications = showNotifications;
window.showProfile = showProfile;
