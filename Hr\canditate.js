// Show candidate details in modal with a more unique, vibrant style and icons
function showCandidateModal(candidateId) {
    const candidate = (window.filteredCandidates || window.candidates || []).find(c => c.id === candidateId);
    if (!candidate) return;

    const detailsDiv = document.getElementById('candidateDetails');
    detailsDiv.innerHTML = `
        <div style="background: linear-gradient(90deg,#ffb347,#ff5e62,#764ba2); padding: 1.5rem; border-radius: 16px; color: #222; box-shadow: 0 4px 24px #ffb34755;">
            <h2 style="margin-bottom:1rem; color:#764ba2; letter-spacing:1px;">
                <span style="font-size:1.5rem;">👤</span> ${candidate.name}
            </h2>
            <p><strong>📧 Email:</strong> <span style="color:#764ba2">${candidate.email}</span></p>
            <p><strong>📱 Phone:</strong> <span style="color:#ff5e62">${candidate.phone}</span></p>
            <p><strong>📍 Location:</strong> <span style="color:#667eea">${candidate.location}</span></p>
            <p><strong>💼 Position:</strong> ${candidate.position}</p>
            <p><strong>⏳ Experience:</strong> ${candidate.experience}</p>
            <p><strong>🏷️ Status:</strong> <span style="color:#fff;background:#764ba2;padding:2px 10px;border-radius:8px;box-shadow:0 2px 8px #764ba255;">${candidate.status}</span></p>
            <p><strong>⭐ Score:</strong> <span style="color:#fff;background:#ff5e62;padding:2px 10px;border-radius:8px;box-shadow:0 2px 8px #ff5e6255;">${candidate.score}%</span></p>
            <p><strong>🛠️ Skills:</strong> ${
                candidate.skills.map(skill =>
                    `<span style="display:inline-block;margin:2px 6px 2px 0;padding:3px 10px;border-radius:12px;background:#667eea;color:#fff;font-size:0.95em;box-shadow:0 1px 4px #667eea33;">${skill}</span>`
                ).join('')
            }</p>
        </div>
    `;
    document.getElementById('candidateModal').style.display = 'block';
}

// Close candidate modal
function closeModal() {
    document.getElementById('candidateModal').style.display = 'none';
}

// Reject candidate (simple demo: just update status and reload table)
function rejectCandidate(candidateId) {
    if (!window.candidates) return;
    const candidate = window.candidates.find(c => c.id === candidateId);
    if (candidate) {
        candidate.status = 'rejected';
        if (typeof loadCandidatesTable === 'function') loadCandidatesTable();
    }
}

// Optional: filter candidates by search input
function filterCandidatesTable() {
    const input = document.getElementById('searchInput');
    const filter = input.value.trim().toLowerCase();
    window.filteredCandidates = window.candidates.filter(candidate =>
        candidate.name.toLowerCase().includes(filter) ||
        candidate.position.toLowerCase().includes(filter) ||
        candidate.location.toLowerCase().includes(filter) ||
        candidate.status.toLowerCase().includes(filter)
    );
    if (typeof loadCandidatesTable === 'function') loadCandidatesTable();
}

// Attach search event
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('searchInput');
    if (input) {
        input.addEventListener('input', filterCandidatesTable);
    }
});