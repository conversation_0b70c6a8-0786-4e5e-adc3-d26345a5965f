/* HR Dashboard - <PERSON>oh<PERSON> Recruit Inspired Design */

/* Enhanced Header */
.hr-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(44, 62, 80, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: white;
    transition: transform 0.2s ease;
}

.logo-link:hover {
    transform: scale(1.02);
    color: white;
}

.logo-icon {
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.company-info h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.company-info p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 500;
}

.header-nav .nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.header-nav .nav-link-zoho {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.header-nav .nav-link-zoho:hover,
.header-nav .nav-link-zoho.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    backdrop-filter: blur(10px);
}

.header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.notification-badge {
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    margin-left: 0.25rem;
}

/* Main Container */
.hr-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--background-tertiary, #fafafa);
    min-height: calc(100vh - 80px);
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-content h3 {
    color: var(--text-secondary, #757575);
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary, #212121);
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

.stat-change.neutral {
    color: var(--text-secondary, #757575);
}

/* Dashboard Content Grid */
.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    grid-template-areas: 
        "applications actions"
        "pipeline activity";
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-tertiary, #fafafa);
}

.card-header h3 {
    color: var(--text-primary, #212121);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

/* Applications List */
.dashboard-card:nth-child(1) {
    grid-area: applications;
}

.applications-list {
    padding: 1rem 2rem 2rem;
}

.application-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.application-item:last-child {
    border-bottom: none;
}

.candidate-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.candidate-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.candidate-details h4 {
    color: var(--text-primary, #212121);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.candidate-details p {
    color: var(--text-secondary, #757575);
    font-size: 0.9rem;
    margin: 0;
}

.application-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.application-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.application-status.new {
    background: #e3f2fd;
    color: #1976d2;
}

.application-status.reviewed {
    background: #fff3e0;
    color: #f57c00;
}

.application-status.interview {
    background: #e8f5e8;
    color: #388e3c;
}

.application-time {
    color: var(--text-secondary, #757575);
    font-size: 0.8rem;
}

/* Quick Actions */
.dashboard-card:nth-child(2) {
    grid-area: actions;
}

.quick-actions {
    padding: 1rem 2rem 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.action-btn:hover {
    border-color: var(--primary-color, #1976d2);
    background: var(--background-tertiary, #fafafa);
}

.action-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--background-secondary, #f5f5f5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-text h4 {
    color: var(--text-primary, #212121);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.action-text p {
    color: var(--text-secondary, #757575);
    font-size: 0.9rem;
    margin: 0;
}

/* Pipeline Card */
.pipeline-card {
    grid-area: pipeline;
}

.pipeline-filter {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
}

.pipeline-stages {
    padding: 1rem 2rem 2rem;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.pipeline-stage {
    flex: 1;
    text-align: center;
}

.pipeline-stage h4 {
    color: var(--text-primary, #212121);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.stage-count {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color, #1976d2);
    margin-bottom: 0.5rem;
}

.stage-bar {
    height: 4px;
    background: var(--border-light, #f0f0f0);
    border-radius: 2px;
    overflow: hidden;
}

.stage-progress {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

/* Activity Feed */
.dashboard-card:nth-child(4) {
    grid-area: activity;
}

.activity-feed {
    padding: 1rem 2rem 2rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--background-secondary, #f5f5f5);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content p {
    color: var(--text-primary, #212121);
    font-size: 0.9rem;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.activity-time {
    color: var(--text-secondary, #757575);
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "applications"
            "actions"
            "pipeline"
            "activity";
    }
    
    .pipeline-stages {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .pipeline-stage {
        display: flex;
        align-items: center;
        justify-content: space-between;
        text-align: left;
    }
    
    .stage-bar {
        width: 100px;
        margin-left: 1rem;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }
    
    .header-nav .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hr-container {
        padding: 1rem;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .applications-list,
    .quick-actions,
    .pipeline-stages,
    .activity-feed {
        padding: 1rem;
    }
}
