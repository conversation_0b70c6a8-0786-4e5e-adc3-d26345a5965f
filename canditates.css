table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    background: linear-gradient(120deg, #f5e6ff 0%, #e0c3fc 100%);
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 6px 24px rgba(118, 75, 162, 0.15);
}
th, td {
    border: none;
    padding: 14px 18px;
    font-size: 1.05rem;
    transition: background 0.3s, color 0.3s;
}
th {
    background: linear-gradient(90deg, #764ba2 60%, #ff5e62 100%);
    color: #fff;
    letter-spacing: 1px;
    font-weight: 700;
    text-shadow: 1px 1px 8px #3332;
}
tr:nth-child(even) {
    background: rgba(255, 179, 71, 0.13);
}
tr:nth-child(odd) {
    background: rgba(102, 126, 234, 0.10);
}
tr:hover {
    background: linear-gradient(90deg, #ffb347 0%, #ff5e62 100%);
    color: #fff;
    box-shadow: 0 2px 16px #ffb34755;
    transform: scale(1.01);
}
td:first-child {
    font-weight: 600;
    color: #764ba2;
}
td:last-child {
    font-weight: 600;
    color: #ff5e62;
}